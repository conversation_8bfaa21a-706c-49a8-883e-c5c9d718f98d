<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.laery0.elm.dao.FoodDao">

    <update id="updateFood">
        update food
        <set>
            <if test="foodName != null and foodName != ''">
                foodName = #{foodName},
            </if>
            <if test="foodExplain != null and foodExplain != ''">
                foodExplain = #{foodExplain},
            </if>
            <if test="foodPrice != null and foodPrice >= 0">
                foodPrice = #{foodPrice},
            </if>
        </set>
        where foodId = #{foodId}
    </update>
</mapper>
