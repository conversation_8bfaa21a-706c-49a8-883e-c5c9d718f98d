<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.laery0.elm.dao.AdminDao">
    <insert id="insertBusiness" parameterType="com.laery0.elm.po.Business" useGeneratedKeys="true" keyProperty="businessId">
        insert  into business (businessName, password) values (#{businessName}, '123')
    </insert>

    <select id="dynamicQueryBusiness" parameterType="com.laery0.elm.po.Business" resultType="com.laery0.elm.po.Business">
        select * from business
        <where>
            <if test="businessName != null and businessName != ''">
                and businessName like concat('%', #{businessName}, '%')
            </if>
            <if test="businessAddress != null and businessAddress != ''">
                and businessAddress like concat('%', #{businessAddress}, '%')
            </if>
        </where>
    </select>
</mapper>