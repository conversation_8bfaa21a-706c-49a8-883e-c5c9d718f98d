<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.laery0.elm.dao.BusinessDao">

    <update id="dynamicUpdateBusiness">
        update business
        <set>
            <if test="businessName != null and businessName != ''">
                businessName = #{businessName},
            </if>
            <if test="businessAddress != null and businessAddress != ''">
                businessAddress = #{businessAddress},
            </if>
            <if test="businessExplain != null and businessExplain != ''">
                businessExplain = #{businessExplain},
            </if>
            <if test="starPrice != null and starPrice >= 0">
                starPrice = #{starPrice},
            </if>
            <if test="deliveryPrice != null and deliveryPrice >= 0">
                deliveryPrice = #{deliveryPrice},
            </if>
        </set>
        where businessId = #{businessId}
    </update>
</mapper>