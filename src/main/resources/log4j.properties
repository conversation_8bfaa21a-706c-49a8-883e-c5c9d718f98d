### direct log messages to stdout ###
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %m%n
### direct messages to file mylog.log ###
#log4j.appender.file=org.apache.log4j.FileAppender
#log4j.appender.file.File=d:/mylog.log
#log4j.appender.file.layout=org.apache.log4j.PatternLayout
#log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %m%n
### set log levels - for more verbose logging change 'debug?info?warn?error' ###
### log4j.rootLogger=debug,stdout,file
log4j.rootLogger=trace,stdout