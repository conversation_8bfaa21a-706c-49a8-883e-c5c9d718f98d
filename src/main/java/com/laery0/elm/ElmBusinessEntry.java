package com.laery0.elm;

import com.laery0.elm.config.SpringConfig;
import com.laery0.elm.view.BusinessView;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

public class ElmBusinessEntry {

	public static void main(String[] args) {
        ApplicationContext context = new AnnotationConfigApplicationContext(SpringConfig.class);
        BusinessView businessView = (BusinessView) context.getBean(BusinessView.class);
        if(businessView.login()) {
            while(true) {
                if(!businessView.choice1()) {
                    break;
                }
            }
        }
	}
}
