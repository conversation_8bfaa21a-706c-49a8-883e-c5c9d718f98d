package com.laery0.elm.dao;

import java.util.List;

import com.laery0.elm.po.Food;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

//食品DAO层接口
@Component
public interface FoodDao {
//	按商家ID查询食品
	@Select("select * from food where businessId = #{businessId}")
	public List<Food> listFoodByBusinessId(Integer businessId);
//	食品上架
	@Insert("insert into food (foodName, foodExplain, foodPrice, businessId) values (#{foodName}, #{foodExplain}, #{foodPrice}, #{businessId})")
	public int saveFood(Food food);
//	按ID查询食品
	@Select("select * from food where foodId = #{foodId}")
	public Food getFoodById(Integer foodId);
//	食品信息维护
	public int updateFood(Food food);
//	食品下架
	@Delete("delete from food where foodId = #{foodId}")
	public int removeFood(Integer foodId);
}
