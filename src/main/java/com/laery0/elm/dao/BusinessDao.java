package com.laery0.elm.dao;

import com.laery0.elm.po.Business;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

//商家DAO层接口
@Component
public interface BusinessDao {
//    商家登录
    @Select("select * from business where businessId = #{businessId} and password = #{password}")
    public Business getBusinessByIdAndPass(Business business);
//    1.查看商家信息
    @Select("select * from business where businessId = #{businessId}")
    public Business selectBusiness(Integer businessId);
//    2.修改商家信息
    public int dynamicUpdateBusiness(Business business);

//    3.更新密码
    @Update("update business set password = #{password} where businessId = #{businessId}")
    public int updatePasswordById(Business business);
}
