package com.laery0.elm.dao;

import com.laery0.elm.po.Admin;
import com.laery0.elm.po.Business;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

//平台管理者DAO层接口
@Component
public interface AdminDao {
//	平台管理者登录
    @Select("select * from admin where adminName = #{adminName} and password = #{password}")
	public Admin getAdminByNameByPass(Admin admin);
//    1.所有商家列表
    @Select("select * from business")
    public List<Business> selectAllBusiness();
//    2.动态搜索商家
    public List<Business> dynamicQueryBusiness(Business business);
//    3.新建商家
    public int insertBusiness(Business business);
//    4.删除商家
    @Delete("delete from business where businessId = #{businessId}")
    public Integer deleteBusinessByBusinessId(Integer businessId);
}
