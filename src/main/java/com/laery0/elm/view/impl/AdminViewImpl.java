package com.laery0.elm.view.impl;

import com.laery0.elm.controller.AdminController;
import com.laery0.elm.po.Admin;
import com.laery0.elm.po.Business;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.view.AdminView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Scanner;

@Component
public class AdminViewImpl implements AdminView {
    @Autowired
    private AdminController adminController;
    @Autowired
    private Scanner scanner;

    @Override
    public void hello() {
        System.out.println("---------------------------------------------------------");
        System.out.println("|\t\t\t 饿了么后台管理系统-平台管理者模块  \t\t\t|");
        System.out.println("---------------------------------------------------------");
    }

    public boolean choice() {
        System.out.println("\n========= 1.所有商家列表=2.搜索商家=3.新建商家=4.删除商家=5.退出系统 =========");
        System.out.print("请输入你的选择：");
        int choice = scanner.nextInt();
        switch(choice) {
            case 1:
                listBusinessAll();
                return true;
            case 2:
                dynamicQueryBusiness();
                return true;
            case 3:
                insertBusiness();
                return true;
            case 4:
                deleteBusinessByBusinessId();
                return true;
            default:
                System.out.println("退出系统");
                return false;
        }
    }

    @Override
    public boolean login() {
        hello();
        Admin admin = new Admin();
        int errorCount = 0;
        while(true) {
            System.out.print("请输入管理员用户名:");
            admin.setAdminName(scanner.next());
            System.out.print("请输入管理员密码:");
            admin.setPassword(scanner.next());
            ResponseBean<Admin> responseBean = adminController.getAdminByNameByPass(admin);
            if(responseBean.getStatus() == 200){
                System.out.println(responseBean.getMsg());
                return true;
            } else {
                errorCount++;
                if(errorCount == 5) {
                    System.out.println("尝试次数过多, 请稍后再尝试");
                    return false;
                }
                System.out.println(responseBean.getMsg() + "(您还有" + (5 - errorCount) + "次机会)");
            }
        }
    }

    @Override
    public void listBusinessAll() {
        ResponseBean<List<Business>> listResponseBean = adminController.selectAllBusiness();
        if(listResponseBean.getStatus()==200){
            List<Business> businessList = listResponseBean.getData();
            for (Business business : businessList) {
                System.out.println(business);
            }
        } else {
            System.out.println(listResponseBean.getMsg());
        }
    }

    @Override
    public void deleteBusinessByBusinessId() {
        System.out.print("输入要删除的商家id:");
        Integer businessId = scanner.nextInt();
        System.out.print("是否确认删除?(y/n)");
        char flag = scanner.next().charAt(0);
        if(flag == 'y') {
            ResponseBean<Integer> responseBean = adminController.deleteBusinessByBusinessId(businessId);
            System.out.println(responseBean.getMsg());
        } else {
            System.out.println("取消删除");
        }
    }

    @Override
    public void dynamicQueryBusiness() {
        Business business = new Business();
        scanner.nextLine();
        System.out.print("请输入商家名称(回车跳过):");
        String nameInput = scanner.nextLine();
        if(nameInput.trim().isEmpty()) {
            business.setBusinessName(null);
        } else {
            business.setBusinessName(nameInput);
        }
        System.out.print("请输入商家地址(回车跳过):");
        String addressInput = scanner.nextLine();
        if(addressInput.trim().isEmpty()) {
            business.setBusinessAddress(null);
        } else {
            business.setBusinessAddress(addressInput);
        }

        ResponseBean<List<Business>> listResponseBean = adminController.dynamicQueryBusiness(business);
        if(listResponseBean.getStatus()==200){
            List<Business> businessList = listResponseBean.getData();
            for (Business business1 : businessList) {
                System.out.println(business1);
            }
        } else {
            System.out.println(listResponseBean.getMsg());
        }
    }

    @Override
    public void insertBusiness() {
        Business business = new Business();
        System.out.print("请输入商家名称:");
        business.setBusinessName(scanner.next());
        int i = adminController.insertBusiness(business);
        System.out.println("新添加的商家id为:" + business.getBusinessId());
    }
}
