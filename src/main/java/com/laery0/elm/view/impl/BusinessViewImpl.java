package com.laery0.elm.view.impl;

import com.laery0.elm.controller.BusinessController;
import com.laery0.elm.po.Business;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.BusinessService;
import com.laery0.elm.view.BusinessView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Scanner;

@Component
public class BusinessViewImpl implements BusinessView {
    @Autowired
    private BusinessController businessController;
    @Autowired
    private Scanner scanner;
    private Business loginBusiness;
    @Autowired
    private BusinessService businessService;

    @Override
    public void hello() {
        System.out.println("---------------------------------------------------------");
        System.out.println("|\t\t\t 饿了么后台管理系统-商家模块  \t\t\t|");
        System.out.println("---------------------------------------------------------");
    }

    @Override
    public boolean login() {
        hello();
        Business business = new Business();
        int errorCount = 0;
        while(true) {
            System.out.print("请输入商家Id:");
            business.setBusinessId(scanner.nextInt());
            System.out.print("请输入商家密码:");
            business.setPassword(scanner.next());
            ResponseBean<Business> responseBean = businessController.getBusinessByIdAndPass(business);
            if(responseBean.getStatus() == 200){
                System.out.println(responseBean.getMsg());
                loginBusiness = responseBean.getData();
                return true;
            } else {
                errorCount++;
                if(errorCount == 5) {
                    System.out.println("尝试次数过多, 请稍后再尝试");
                    return false;
                }
                System.out.println(responseBean.getMsg() + "(您还有" + (5 - errorCount) + "次机会)");
            }
        }
    }

    @Override
    public boolean choice1() {
        System.out.println("\n======= 一级菜单（商家管理）1.查看商家信息=2.修改商家信息=3.更新密码=4.所属商品管理=5.退出系统=======");
        System.out.print("请输入你的选择：");
        int choice = scanner.nextInt();
        switch(choice) {
            case 1:
                selectBusiness();
                return true;
            case 2:
                dynamicUpdateBusiness();
                return true;
            case 3:
                updatePasswordById();
                return true;
            default:
                System.out.println("退出系统");
                return false;
        }
    }

    @Override
    public void selectBusiness() {
        System.out.println(businessController.selectBusiness(loginBusiness.getBusinessId()));
    }

    @Override
    public void dynamicUpdateBusiness() {
        Business business = new Business();
        business.setBusinessId(loginBusiness.getBusinessId());
        scanner.nextLine();
        System.out.print("输入商家名称:(回车跳过)");
        String name = scanner.nextLine();
        if (!name.isEmpty()) {
            business.setBusinessName(name);
        }
        System.out.print("输入商家介绍:(回车跳过)");
        String explain = scanner.nextLine();
        if (!explain.isEmpty()) {
            business.setBusinessExplain(explain);
        }
        System.out.print("输入商家地址:(回车跳过)");
        String address = scanner.nextLine();
        if (!address.isEmpty()) {
            business.setBusinessAddress(address);
        }
        System.out.print("输入商家起送费:(回车跳过)");
        String starPriceInput = scanner.nextLine();
        if (!starPriceInput.isEmpty()) {
            try {
                double starPrice = Double.parseDouble(starPriceInput);
                business.setStarPrice(starPrice);
            } catch (NumberFormatException e) {
                System.out.println("输入格式错误，起送费保持原值");
            }
        }
        System.out.print("输入商家配送费:(回车跳过)");
        String deliveryPriceInput = scanner.nextLine();
        if (!deliveryPriceInput.isEmpty()) {
            try {
                double deliveryPrice = Double.parseDouble(deliveryPriceInput);
                business.setDeliveryPrice(deliveryPrice);
            } catch (NumberFormatException e) {
                System.out.println("输入格式错误，配送费保持原值");
            }
        }
        businessController.dynamicUpdateBusiness(business);
        System.out.println("更新成功");
    }

    @Override
    public void updatePasswordById() {
        System.out.print("请输入旧密码:");
        String password = scanner.next();
        if(password.equals(loginBusiness.getPassword())){
            System.out.print("请输入新密码:");
            String newPassword1 = scanner.next();
            System.out.print("请再次输入新密码:");
            String newPassword2 = scanner.next();
            if(newPassword1.equals(newPassword2)){
                Business business = new Business();
                business.setBusinessId(loginBusiness.getBusinessId());
                business.setPassword(newPassword1);
                businessController.updatePasswordById(business);
                System.out.println("更新成功");
            }else {
                System.out.println("输入的新密码两次不一致, 更新失败");
            }
        }else {
            System.out.println("密码错误, 更新失败");
        }
    }
}
