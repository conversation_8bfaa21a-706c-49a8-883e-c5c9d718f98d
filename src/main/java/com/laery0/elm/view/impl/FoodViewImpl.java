package com.laery0.elm.view.impl;

import com.laery0.elm.controller.FoodController;
import com.laery0.elm.po.Food;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.view.FoodView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Scanner;

@Component
public class FoodViewImpl implements FoodView {
    @Autowired
    private FoodController foodController;
    @Autowired
    private Scanner scanner;

    @Override
    public List<Food> showFoodList(Integer businessId) {
        ResponseBean<List<Food>> responseBean = foodController.listFoodByBusinessId(businessId);
        if(responseBean.getStatus() == 200) {
            List<Food> foodList = responseBean.getData();
            System.out.println("=== 食品列表 ===");
            for (Food food : foodList) {
                System.out.println(food);
            }
            return foodList;
        } else {
            System.out.println(responseBean.getMsg());
            return null;
        }
    }

    @Override
    public void saveFood(Integer businessId) {
        Food food = new Food();
        food.setBusinessId(businessId);
        
        System.out.print("请输入食品名称:");
        food.setFoodName(scanner.next());
        
        scanner.nextLine(); // 消费换行符
        System.out.print("请输入食品介绍:");
        food.setFoodExplain(scanner.nextLine());
        
        System.out.print("请输入食品价格:");
        try {
            double price = scanner.nextDouble();
            food.setFoodPrice(price);
            
            ResponseBean<Integer> responseBean = foodController.saveFood(food);
            System.out.println(responseBean.getMsg());
        } catch (Exception e) {
            System.out.println("价格输入格式错误，请输入数字");
        }
    }

    @Override
    public void updateFood(Integer businessId) {
        // 先显示当前商家的食品列表
        List<Food> foodList = showFoodList(businessId);
        if(foodList == null || foodList.isEmpty()) {
            return;
        }
        
        System.out.print("请输入要修改的食品编号:");
        try {
            Integer foodId = scanner.nextInt();
            
            // 检查食品是否属于当前商家
            boolean found = false;
            for (Food food : foodList) {
                if (food.getFoodId().equals(foodId)) {
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                System.out.println("该食品不属于当前商家或不存在");
                return;
            }
            
            Food food = new Food();
            food.setFoodId(foodId);
            
            scanner.nextLine(); // 消费换行符
            System.out.print("输入食品名称:(回车跳过)");
            String name = scanner.nextLine();
            if (!name.isEmpty()) {
                food.setFoodName(name);
            }
            
            System.out.print("输入食品介绍:(回车跳过)");
            String explain = scanner.nextLine();
            if (!explain.isEmpty()) {
                food.setFoodExplain(explain);
            }
            
            System.out.print("输入食品价格:(回车跳过)");
            String priceInput = scanner.nextLine();
            if (!priceInput.isEmpty()) {
                try {
                    double price = Double.parseDouble(priceInput);
                    food.setFoodPrice(price);
                } catch (NumberFormatException e) {
                    System.out.println("输入格式错误，价格保持原值");
                }
            }
            
            ResponseBean<Integer> responseBean = foodController.updateFood(food);
            System.out.println(responseBean.getMsg());
        } catch (Exception e) {
            System.out.println("输入格式错误");
        }
    }

    @Override
    public void removeFood(Integer businessId) {
        // 先显示当前商家的食品列表
        List<Food> foodList = showFoodList(businessId);
        if(foodList == null || foodList.isEmpty()) {
            return;
        }
        
        System.out.print("请输入要删除的食品编号:");
        try {
            Integer foodId = scanner.nextInt();
            
            // 检查食品是否属于当前商家
            boolean found = false;
            for (Food food : foodList) {
                if (food.getFoodId().equals(foodId)) {
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                System.out.println("该食品不属于当前商家或不存在");
                return;
            }
            
            System.out.print("是否确认删除?(y/n)");
            char flag = scanner.next().charAt(0);
            if(flag == 'y') {
                ResponseBean<Integer> responseBean = foodController.removeFood(foodId);
                System.out.println(responseBean.getMsg());
            } else {
                System.out.println("取消删除");
            }
        } catch (Exception e) {
            System.out.println("输入格式错误");
        }
    }
    
    // 二级菜单方法
    public boolean choice2(Integer businessId) {
        System.out.println("\n======= 二级菜单（食品管理）1.查看食品列表=2.新增食品=3.修改食品=4.删除食品=5.返回一级菜单 =======");
        System.out.print("请输入你的选择：");
        try {
            int choice = scanner.nextInt();
            switch(choice) {
                case 1:
                    showFoodList(businessId);
                    return true;
                case 2:
                    saveFood(businessId);
                    return true;
                case 3:
                    updateFood(businessId);
                    return true;
                case 4:
                    removeFood(businessId);
                    return true;
                case 5:
                    System.out.println("返回一级菜单");
                    return false;
                default:
                    System.out.println("无效选择，请重新输入");
                    return true;
            }
        } catch (Exception e) {
            System.out.println("输入格式错误，请输入数字");
            scanner.nextLine(); // 清除错误输入
            return true;
        }
    }
}
