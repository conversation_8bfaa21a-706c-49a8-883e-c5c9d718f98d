package com.laery0.elm;

import com.laery0.elm.config.SpringConfig;
import com.laery0.elm.view.AdminView;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

public class ElmAdminEntry {
	public static void main(String[] args) {
        ApplicationContext context = new AnnotationConfigApplicationContext(SpringConfig.class);
        AdminView adminView = (AdminView) context.getBean(AdminView.class);
        if(adminView.login()) {
            while(true) {
                if(!adminView.choice()) {
                    break;
                }
            }
        }
	}
}
