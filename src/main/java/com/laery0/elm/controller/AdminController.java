package com.laery0.elm.controller;

import com.laery0.elm.po.Admin;
import com.laery0.elm.po.Business;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.List;

//平台管理者控制器
@Controller
public class AdminController {
//	平台管理者服务层
    @Autowired
	private AdminService adminService;

//	平台管理者登录方法
	public ResponseBean<Admin> getAdminByNameByPass(Admin admin){
        return adminService.getAdminByNameAndPass(admin);
	}

    //    查询所有商家
    public ResponseBean<List<Business>> selectAllBusiness(){
        return adminService.selectAllBusiness();
    }

    public ResponseBean<Integer> deleteBusinessByBusinessId(Integer businessId) {
        return adminService.deleteBusinessByBusinessId(businessId);
    }

    public ResponseBean<List<Business>> dynamicQueryBusiness(Business business) {
        return adminService.dynamicQueryBusiness(business);
    }

    public int insertBusiness(Business business) {
        return adminService.insertBusiness(business);
    }
}
