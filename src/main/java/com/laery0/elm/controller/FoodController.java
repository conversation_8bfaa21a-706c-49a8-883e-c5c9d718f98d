package com.laery0.elm.controller;


import java.util.List;

import com.laery0.elm.po.Food;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.FoodService;
//食品控制器
public class FoodController {
//	食品服务层
	private FoodService fs;
//	按商家ID查询食品
	public ResponseBean<List<Food>> listFoodByBusinessId(Integer businessId){
		return null;
	}
//	食品上架
	public ResponseBean<Integer> saveFood(Food food){
		return null;
	}
//	按ID查询食品信息
	public ResponseBean<Food> getFoodById(Integer foodId){
		return null;
	}
//	更新维护食品信息
	public ResponseBean<Integer> updateFood(Food food){
		return null;
	}
//	食品下架
	public ResponseBean<Integer> removeFood(Integer foodId){
		return null;
	}



}
