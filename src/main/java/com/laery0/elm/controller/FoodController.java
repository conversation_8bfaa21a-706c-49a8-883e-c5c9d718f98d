package com.laery0.elm.controller;


import java.util.List;

import com.laery0.elm.po.Food;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.FoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

//食品控制器
@Controller
public class FoodController {
//	食品服务层
	@Autowired
	private FoodService foodService;
//	按商家ID查询食品
	public ResponseBean<List<Food>> listFoodByBusinessId(Integer businessId){
		return foodService.listFoodByBusinessId(businessId);
	}
//	食品上架
	public ResponseBean<Integer> saveFood(Food food){
		return foodService.saveFood(food);
	}
//	按ID查询食品信息
	public ResponseBean<Food> getFoodById(Integer foodId){
		return foodService.getFoodById(foodId);
	}
//	更新维护食品信息
	public ResponseBean<Integer> updateFood(Food food){
		return foodService.updateFood(food);
	}
//	食品下架
	public ResponseBean<Integer> removeFood(Integer foodId){
		return foodService.removeFood(foodId);
	}
}
