package com.laery0.elm.controller;

import com.laery0.elm.po.Business;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.BusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

//商家控制器
@Controller
public class BusinessController {
    @Autowired
    BusinessService businessService;

    public ResponseBean<Business> getBusinessByIdAndPass(Business business) {
        return businessService.getBusinessByIdAndPass(business);
    }

    public Business selectBusiness(Integer businessId) {
        return businessService.selectBusiness(businessId);
    }

    public int dynamicUpdateBusiness(Business business) {
        return businessService.dynamicUpdateBusiness(business);
    }

    public int updatePasswordById(Business business) {
        return  businessService.updatePasswordById(business);
    }
}
