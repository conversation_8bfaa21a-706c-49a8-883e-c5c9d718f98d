package com.laery0.elm.service;

import java.util.List;

import com.laery0.elm.po.Food;
import com.laery0.elm.po.ResponseBean;
//食品服务层接口
public interface FoodService {
//	按商家ID查询食品
	public ResponseBean<List<Food>> listFoodByBusinessId(Integer businessId);
//	食品上架
	public ResponseBean<Integer> saveFood(Food food);
//	按ID查询食品信息
	public ResponseBean<Food> getFoodById(Integer foodId);
//	更新维护食品信息
	public ResponseBean<Integer> updateFood(Food food);
//	食品下架
	public ResponseBean<Integer> removeFood(Integer foodId);
}
