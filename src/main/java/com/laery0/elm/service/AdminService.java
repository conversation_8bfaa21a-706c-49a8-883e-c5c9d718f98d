package com.laery0.elm.service;

import com.laery0.elm.po.Admin;
import com.laery0.elm.po.Business;
import com.laery0.elm.po.ResponseBean;

import java.util.List;

//平台管理者服务层接口
public interface AdminService {
//	平台管理者登录方法
	public ResponseBean<Admin> getAdminByNameAndPass(Admin admin);

    public ResponseBean<List<Business>> selectAllBusiness();

    public ResponseBean<Integer> deleteBusinessByBusinessId(Integer businessId);

    public ResponseBean<List<Business>> dynamicQueryBusiness(Business business);

    public int insertBusiness(Business business);
}
