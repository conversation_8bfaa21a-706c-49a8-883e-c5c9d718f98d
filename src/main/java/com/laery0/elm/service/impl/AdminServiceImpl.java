package com.laery0.elm.service.impl;

import com.laery0.elm.dao.AdminDao;
import com.laery0.elm.po.Admin;
import com.laery0.elm.po.Business;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AdminServiceImpl implements AdminService {
    @Autowired
    private AdminDao adminDao;

    @Override
    public ResponseBean<Admin> getAdminByNameAndPass(Admin admin) {
        Admin adminByNameByPass = adminDao.getAdminByNameByPass(admin);
        if  (adminByNameByPass == null) {
            return new ResponseBean<>(201,  "用户名或密码错误, 请重试");
        } else {
            return new ResponseBean<>(adminByNameByPass, 200,  "登录成功");
        }
    }

    @Override
    public ResponseBean<List<Business>> selectAllBusiness() {
        List<Business> businessList = adminDao.selectAllBusiness();
        if(businessList.isEmpty()) {
            return new ResponseBean<>(201, "当前没有任何商家");
        } else {
            return new ResponseBean<>(businessList, 200, "查询成功");
        }
    }

    @Override
    public ResponseBean<Integer> deleteBusinessByBusinessId(Integer businessId) {
        Integer integer = adminDao.deleteBusinessByBusinessId(businessId);
        if(integer == null) {
            return new ResponseBean<>(202, "系统后台错误");
        } else if (integer == 0) {
            return new ResponseBean<>(201, "没有找到id为" + businessId + "的商家");
        } else {
            return new ResponseBean<>(integer, 200, "删除成功");
        }
    }

    @Override
    public ResponseBean<List<Business>> dynamicQueryBusiness(Business business) {
        List<Business> businessList = adminDao.dynamicQueryBusiness(business);
        if(businessList.isEmpty()) {
            return new ResponseBean<>(201, "未找到匹配的商家");
        } else {
            return new ResponseBean<>(businessList, 200, "查询成功");
        }
    }

    @Override
    public int insertBusiness(Business business) {
        return adminDao.insertBusiness(business);
    }
}
