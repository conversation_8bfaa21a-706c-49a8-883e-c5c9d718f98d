package com.laery0.elm.service.impl;

import com.laery0.elm.dao.BusinessDao;
import com.laery0.elm.po.Business;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.BusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BusinessServiceImpl implements BusinessService {
    @Autowired
    private BusinessDao businessDao;

    @Override
    public ResponseBean<Business> getBusinessByIdAndPass(Business business) {
        Business businessByIdAndPass = businessDao.getBusinessByIdAndPass(business);
        if(businessByIdAndPass==null) {
            return new ResponseBean<>(201,  "用户名或密码错误, 请重试");
        } else {
            return new ResponseBean<>(businessByIdAndPass, 200,  "登录成功");
        }
    }

    @Override
    public Business selectBusiness(Integer businessId) {
        return businessDao.selectBusiness(businessId);
    }

    @Override
    public int dynamicUpdateBusiness(Business business) {
        return businessDao.dynamicUpdateBusiness(business);
    }

    @Override
    public int updatePasswordById(Business business) {
        return  businessDao.updatePasswordById(business);
    }
}
