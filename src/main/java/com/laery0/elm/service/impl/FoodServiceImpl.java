package com.laery0.elm.service.impl;

import com.laery0.elm.dao.FoodDao;
import com.laery0.elm.po.Food;
import com.laery0.elm.po.ResponseBean;
import com.laery0.elm.service.FoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FoodServiceImpl implements FoodService {
    @Autowired
    private FoodDao foodDao;

    @Override
    public ResponseBean<List<Food>> listFoodByBusinessId(Integer businessId) {
        List<Food> foodList = foodDao.listFoodByBusinessId(businessId);
        if(foodList.isEmpty()) {
            return new ResponseBean<>(201, "当前商家没有任何食品");
        } else {
            return new ResponseBean<>(foodList, 200, "查询成功");
        }
    }

    @Override
    public ResponseBean<Integer> saveFood(Food food) {
        int result = foodDao.saveFood(food);
        if(result > 0) {
            return new ResponseBean<>(result, 200, "食品上架成功");
        } else {
            return new ResponseBean<>(201, "食品上架失败");
        }
    }

    @Override
    public ResponseBean<Food> getFoodById(Integer foodId) {
        Food food = foodDao.getFoodById(foodId);
        if(food == null) {
            return new ResponseBean<>(201, "未找到该食品");
        } else {
            return new ResponseBean<>(food, 200, "查询成功");
        }
    }

    @Override
    public ResponseBean<Integer> updateFood(Food food) {
        int result = foodDao.updateFood(food);
        if(result > 0) {
            return new ResponseBean<>(result, 200, "食品信息更新成功");
        } else {
            return new ResponseBean<>(201, "食品信息更新失败");
        }
    }

    @Override
    public ResponseBean<Integer> removeFood(Integer foodId) {
        int result = foodDao.removeFood(foodId);
        if(result > 0) {
            return new ResponseBean<>(result, 200, "食品下架成功");
        } else {
            return new ResponseBean<>(201, "食品下架失败，未找到该食品");
        }
    }
}
